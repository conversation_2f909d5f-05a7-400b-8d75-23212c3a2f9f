function saveCommercialProposal() {
  var spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  var activeSheet = spreadsheet.getActiveSheet();
  var activeRow = activeSheet.getActiveRange().getRow();
  var activeSheetLastColumn = activeSheet.getLastColumn();
  var activeSheetHeaders = activeSheet.getRange(1, 1, 1, activeSheetLastColumn).getValues()[0];
  var activeRowData = activeSheet.getRange(activeRow, 1, 1, activeSheetLastColumn).getValues()[0];

  var commercialProposalsSheet = spreadsheet.getSheetByName("Комерційні пропозиції");
  if (!commercialProposalsSheet) {
    Logger.log("Аркуш 'Комерційні пропозиції' не знайдено.");
    return;
  }

  var commercialProposalsHeaders = commercialProposalsSheet.getRange(1, 1, 1, commercialProposalsSheet.getLastColumn()).getValues()[0];
  var dataToInsert = new Array(commercialProposalsHeaders.length).fill(""); // Ініціалізуємо масив порожніми значеннями
  var dateTime = new Date();
  var activeSheetName = activeSheet.getName(); // Отримуємо назву активного аркуша

  // Створюємо мапу для швидкого пошуку індексів стовпців в активному аркуші за їхніми назвами
  var activeSheetHeaderMap = {};
  for (var i = 0; i < activeSheetHeaders.length; i++) {
    activeSheetHeaderMap[activeSheetHeaders[i]] = i;
  }

  // Заповнюємо dataToInsert, зіставляючи стовпці за назвами
  for (var i = 0; i < commercialProposalsHeaders.length; i++) {
    var header = commercialProposalsHeaders[i];
    if (header === "Дата і час") {
      dataToInsert[i] = dateTime;
    } else if (header === "Аркуш") {
      dataToInsert[i] = activeSheetName;
    } else if (header === "Менеджер") { // Додаємо логіку для стовпця "Акаунт"
      dataToInsert[i] = Session.getActiveUser().getEmail();
    } else {
      var activeSheetColumnIndex = activeSheetHeaderMap[header];
      if (activeSheetColumnIndex !== undefined) {
        dataToInsert[i] = activeRowData[activeSheetColumnIndex];
      }
    }
  }

  var lastRow = commercialProposalsSheet.getLastRow();
  var newRowIndex = lastRow + 1;

  commercialProposalsSheet.getRange(newRowIndex, 1, 1, dataToInsert.length).setValues([dataToInsert]);
}
