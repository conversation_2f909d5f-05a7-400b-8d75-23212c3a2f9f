function translateCityNamesToUkr() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getActiveSheet(); // або .getSheetByName('YourSheetName')
  
  // Зчитуємо заголовки
  const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn())
                       .getValues()[0];
  
  // Знаходимо індекси колонок
  const srcCol = headers.indexOf('city_ascii') + 1;
  let tgtCol = headers.indexOf('Назва Українською') + 1;
  
  // Якщо колонки з перекладом немає — створюємо в кінці
  if (tgtCol === 0) {
    tgtCol = headers.length + 1;
    sheet.getRange(1, tgtCol).setValue('Назва Українською');
  }
  
  // Діапазон даних (всі рядки починаючи з 2-го)
  const lastRow = sheet.getLastRow();
  if (lastRow < 2 || srcCol === 0) {
    Logger.log('Немає даних або не знайдено колонку city_ascii');
    return;
  }
  
  const srcRange = sheet.getRange(2, srcCol, lastRow - 1, 1);
  const srcValues = srcRange.getValues();
  
  // Підготуємо масив для перекладів
  const outValues = srcValues.map(row => {
    const city = row[0];
    if (city) {
      try {
        return [ LanguageApp.translate(city, 'en', 'uk') ];
      } catch (e) {
        Logger.log(`Помилка перекладу "${city}": ${e}`);
        return [''];
      }
    } else {
      return [''];
    }
  });
  
  // Записуємо результат
  sheet.getRange(2, tgtCol, outValues.length, 1)
       .setValues(outValues);
  
  Logger.log(`Перекладено ${outValues.filter(r=>r[0]).length} назв.`);
}
