/**
 * @OnlyCurrentDoc
 */

function fillDistancesFromRoutes() {
  const ui = SpreadsheetApp.getUi();
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const activeSheet = spreadsheet.getActiveSheet();
  const activeRange = activeSheet.getActiveRange();
  const activeRowIndex = activeRange.getRow();

  // 1. Перевірка, чи вибрано рівно один рядок
  if (activeRange.getNumRows() !== 1) {
    ui.alert('Будь ласка, виберіть один рядок для обробки.');
    return;
  }

  // Отримуємо всі значення з активного рядка, незалежно від вибраної клітинки
  const rowValues = activeSheet.getRange(activeRowIndex, 1, 1, activeSheet.getLastColumn()).getValues()[0];
  const headers = activeSheet.getRange(1, 1, 1, activeSheet.getLastColumn()).getValues()[0].map(h => String(h).trim());

  // Допоміжна функція для пошуку індексу стовпця за назвою (регістронезалежно)
  function findColumnIndex(headerNames, targetColumnName) {
    for (let i = 0; i < headerNames.length; i++) {
      if (headerNames[i].toLowerCase() === targetColumnName.toLowerCase()) {
        return i;
      }
    }
    return -1;
  }

  // 2. Доступ до аркуша "Маршрути 3"
  const routesSheet = spreadsheet.getSheetByName("Маршрути 3");
  if (!routesSheet) {
    ui.alert('Аркуш "Маршрути 3" не знайдено. Будь ласка, створіть його.');
    return;
  }

  // 3. Отримання даних та заголовків з аркуша "Маршрути 3"
  const routesData = routesSheet.getDataRange().getValues();
  const routesHeaders = routesData.length > 0 ? routesData[0].map(h => String(h).trim()) : [];

  const fromColIndexRoutes = findColumnIndex(routesHeaders, 'Від');
  const toColIndexRoutes = findColumnIndex(routesHeaders, 'До');
  const distanceKmColIndexRoutes = findColumnIndex(routesHeaders, 'Відстань км.');
  const tollManualColIndexRoutes = findColumnIndex(routesHeaders, 'Платна дорога €'); // Додано
  const tollCalculatedColIndexRoutes = findColumnIndex(routesHeaders, 'Платна дорога € розрахована'); // Додано

  if (fromColIndexRoutes === -1 || toColIndexRoutes === -1 || distanceKmColIndexRoutes === -1 || tollManualColIndexRoutes === -1 || tollCalculatedColIndexRoutes === -1) {
    ui.alert('Не вдалося знайти всі необхідні стовпці в аркуші "Маршрути 3": "Від", "До", "Відстань км.", "Платна дорога €", "Платна дорога € розрахована". Перевірте назви.');
    return;
  }

  const loadedRoadsColIndex = findColumnIndex(headers, 'Дороги з вантажем'); // Додано
  const emptyRoadsColIndex = findColumnIndex(headers, 'Дороги порожній'); // Додано

  if (loadedRoadsColIndex === -1 || emptyRoadsColIndex === -1) {
    ui.alert('Не вдалося знайти всі необхідні стовпці в активному аркуші: "Дороги з вантажем", "Дороги порожній". Перевірте назви.');
    return;
  }

  let messages = [];
  let totalLoadedTollCost = 0;
  let totalEmptyTollCost = 0;

  // Допоміжна функція для пошуку та отримання даних маршруту
  function getRouteData(sourceCityColName, destCityColName, targetDistanceColName) {
    const sourceCityColIndex = findColumnIndex(headers, sourceCityColName);
    const destCityColIndex = findColumnIndex(headers, destCityColName);
    const targetDistanceColIndex = findColumnIndex(headers, targetDistanceColName);

    if (sourceCityColIndex === -1 || destCityColIndex === -1 || targetDistanceColIndex === -1) {
      return { success: false, message: `Не знайдено всі необхідні стовпці в активному аркуші для маршруту ${sourceCityColName} - ${destCityColName}.` };
    }

    const sourceCity = String(rowValues[sourceCityColIndex] || '').trim();
    const destinationCity = String(rowValues[destCityColIndex] || '').trim();

    if (!sourceCity || !destinationCity) {
      return { success: false, message: `Порожні значення міст для маршруту ${sourceCityColName} - ${destCityColName}.` };
    }

    for (let i = 1; i < routesData.length; i++) {
      const routeRow = routesData[i];
      if (String(routeRow[fromColIndexRoutes] || '').trim() === sourceCity && String(routeRow[toColIndexRoutes] || '').trim() === destinationCity) {
        const foundDistance = String(routeRow[distanceKmColIndexRoutes] || '').trim();
        
        // Отримуємо вартість платних доріг
        let manualTollCost = parseFloat(String(routeRow[tollManualColIndexRoutes] || '').trim().replace(',', '.')) || 0;
        let calculatedTollCost = parseFloat(String(routeRow[tollCalculatedColIndexRoutes] || '').trim().replace(',', '.')) || 0;
        
        let tollCostToUse = 0;
        if (manualTollCost > 0) {
          tollCostToUse = manualTollCost;
        } else if (calculatedTollCost > 0) {
          tollCostToUse = calculatedTollCost;
        }

        return { success: true, distance: foundDistance, tollCost: tollCostToUse, sourceCity: sourceCity, destinationCity: destinationCity, targetDistanceColName: targetDistanceColName };
      }
    }

    return { success: false, message: `Маршрут ${sourceCity} - ${destinationCity} не знайдено в аркуші "Маршрути 3".` };
  }

  // Обробка маршрутів
  const routesToProcess = [
    { source: 'Подача', dest: 'Завантаження', distCol: 'Подача км.', type: 'empty' },
    { source: 'Завантаження', dest: 'Кордон', distCol: 'До кордону км.', type: 'loaded' },
    { source: 'Кордон', dest: 'Розвантаження', distCol: 'Після кордону км.', type: 'loaded' },
    { source: 'Розвантаження', dest: 'Повернення', distCol: 'Повернення км.', type: 'empty' }
  ];

  routesToProcess.forEach(routeInfo => {
    const result = getRouteData(routeInfo.source, routeInfo.dest, routeInfo.distCol);
    if (result.success) {
      // Заповнюємо відстань
      const targetDistanceColIndex = findColumnIndex(headers, routeInfo.distCol);
      activeSheet.getRange(activeRowIndex, targetDistanceColIndex + 1).setValue(result.distance);
      messages.push(`${result.sourceCity} - ${result.destinationCity}: Відстань ${result.distance} км. записано в '${routeInfo.distCol}'.`);

      // Сумуємо вартість платних доріг
      if (routeInfo.type === 'loaded') {
        totalLoadedTollCost += result.tollCost;
      } else {
        totalEmptyTollCost += result.tollCost;
      }
    } else {
      messages.push(result.message);
    }
  });

  // Записуємо загальну вартість платних доріг
  activeSheet.getRange(activeRowIndex, loadedRoadsColIndex + 1).setValue(totalLoadedTollCost);
  activeSheet.getRange(activeRowIndex, emptyRoadsColIndex + 1).setValue(totalEmptyTollCost);
  messages.push(`Загальна вартість платних доріг з вантажем: ${totalLoadedTollCost} €.`);
  messages.push(`Загальна вартість платних доріг порожній: ${totalEmptyTollCost} €.`);

  // Виводимо повідомлення
  if (messages.length > 0) {
    ui.alert('Результати заповнення:\n\n' + messages.join('\n'));
  } else {
    ui.alert('Усі дані успішно заповнено.');
  }
}
