# Product Context

## Purpose
Цей проект, ймовірно, призначений для оптимізації логістичних або комерційних процесів, що вимагають роботи з маршрутами та географічними даними. Він може бути використаний для:
- Швидкого створення та розрахунку маршрутів.
- Автоматизації заповнення комерційних пропозицій.
- Управління базою даних міст та відстаней.

## Problem Solved
- Зменшення ручної праці при створенні маршрутів та комерційних пропозицій.
- Забезпечення точності географічних даних та розрахунків відстаней.
- Централізоване управління даними, пов'язаними з логістикою.

## How it Should Work
Система повинна дозволяти користувачам:
- Вводити дані про міста та маршрути.
- Автоматично отримувати географічні дані (координати, назви міст).
- Розраховувати відстані між містами.
- Зберігати та завантажувати маршрути.
- Генерувати комерційні пропозиції на основі маршрутів.
- Створювати унікальні посилання на карти для маршрутів.

## User Experience Goals
- Інтуїтивно зрозумілий інтерфейс (через Google Sheets або інший інтерфейс Apps Script).
- Швидке виконання операцій.
- Надійність та точність даних.
- Можливість легко інтегрувати з іншими системами Google Workspace.
