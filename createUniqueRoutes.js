function createUniqueRoutes() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();

  const sourceSheet = ss.getSheetByName(CONSTANTS.SHEET_NAMES.REGISTER_INTERNATIONAL);
  const targetSheet = ss.getSheetByName(CONSTANTS.SHEET_NAMES.ROUTES_2);

  if (!sourceSheet) {
    Logger.log(`Аркуш '${CONSTANTS.SHEET_NAMES.REGISTER_INTERNATIONAL}' не знайдено.`);
    return;
  }
  if (!targetSheet) {
    Logger.log(`Аркуш '${CONSTANTS.SHEET_NAMES.ROUTES_2}' не знайдено.`);
    return;
  }

  const sourceData = sourceSheet.getDataRange().getValues();
  const headers = sourceData[0]; // Припускаємо, що перший рядок - це заголовки

  // Знаходимо індекси потрібних стовпців
  const podachaColIndex = headers.indexOf("Подача");
  const zagruzkaColIndex = headers.indexOf("Завантаження");
  const podachaKmColIndex = headers.indexOf("Подача км.");

  if (podachaColIndex === -1 || zagruzkaColIndex === -1 || podachaKmColIndex === -1) {
    Logger.log("Один або кілька необхідних стовпців ('Подача', 'Завантаження', 'Подача км.') не знайдено.");
    return;
  }

  const uniqueRoutes = new Set();
  const routesArray = [];

  // Починаємо з 1, щоб пропустити заголовки
  for (let i = 1; i < sourceData.length; i++) {
    const row = sourceData[i];
    const podacha = row[podachaColIndex];
    const zagruzka = row[zagruzkaColIndex];
    const podachaKm = row[podachaKmColIndex];

    // Створюємо унікальний ключ для комбінації
    const routeKey = `${podacha}|${zagruzka}|${podachaKm}`;

    if (!uniqueRoutes.has(routeKey)) {
      uniqueRoutes.add(routeKey);
      routesArray.push([podacha, zagruzka, podachaKm]);
    }
  }

  if (routesArray.length > 0) {
    // Додаємо дані в останній незаповнений рядок
    targetSheet.getRange(targetSheet.getLastRow() + 1, 1, routesArray.length, routesArray[0].length).setValues(routesArray);
    Logger.log(`Унікальні маршрути успішно записано в аркуш 'Маршрути 2'. Записано ${routesArray.length} унікальних маршрутів.`);
  } else {
    Logger.log("Не знайдено унікальних маршрутів для запису.");
  }
}
