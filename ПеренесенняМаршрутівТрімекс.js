/**
 * @OnlyCurrentDoc
 */

function transferTrimeksRoutes() {
  const ui = SpreadsheetApp.getUi();
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();

  const sourceSheetName = "Реєстр Трімекс";
  const destinationSheetName = "Маршрути 3";

  const sourceSheet = spreadsheet.getSheetByName(sourceSheetName);
  if (!sourceSheet) {
    ui.alert(`Аркуш "${sourceSheetName}" не знайдено.`);
    return;
  }

  const destinationSheet = spreadsheet.getSheetByName(destinationSheetName);
  if (!destinationSheet) {
    ui.alert(`Аркуш "${destinationSheetName}" не знайдено. Будь ласка, створіть його.`);
    return;
  }

  const sourceData = sourceSheet.getDataRange().getValues();
  if (sourceData.length < 2) {
    ui.alert(`Аркуш "${sourceSheetName}" не містить даних (лише заголовки або порожній).`);
    return;
  }
  const sourceHeaders = sourceData[0].map(h => String(h).trim());

  const destData = destinationSheet.getDataRange().getValues();
  const destHeaders = destData.length > 0 ? destData[0].map(h => String(h).trim()) : [];

  // Допоміжна функція для пошуку індексу стовпця за назвою (регістронезалежно)
  function findColumnIndex(headerNames, targetColumnName) {
    for (let i = 0; i < headerNames.length; i++) {
      if (headerNames[i].toLowerCase() === targetColumnName.toLowerCase()) {
        return i;
      }
    }
    return -1;
  }

  // Індекси стовпців у вихідному аркуші
  const loadCityColIndex = findColumnIndex(sourceHeaders, 'Завантаження');
  const unloadCityColIndex = findColumnIndex(sourceHeaders, 'Розвантаження');
  const routeDistanceColIndex = findColumnIndex(sourceHeaders, 'По маршруту км');
  const paidRoadColIndex = findColumnIndex(sourceHeaders, 'Дороги доставка');

  if (loadCityColIndex === -1 || unloadCityColIndex === -1 || routeDistanceColIndex === -1 || paidRoadColIndex === -1) {
    ui.alert(`Не вдалося знайти всі необхідні стовпці в аркуші "${sourceSheetName}": 'Завантаження', 'Розвантаження', 'По маршруту км', 'Дороги доставка'. Перевірте назви.`);
    return;
  }

  // Індекси стовпців у цільовому аркуші "Маршрути 3"
  const destFromColIndex = findColumnIndex(destHeaders, 'Від');
  const destToColIndex = findColumnIndex(destHeaders, 'До');
  const destDistanceKmColIndex = findColumnIndex(destHeaders, 'Відстань км.');
  const destPaidRoadColIndex = findColumnIndex(destHeaders, 'Платна дорога €');

  if (destFromColIndex === -1 || destToColIndex === -1 || destDistanceKmColIndex === -1 || destPaidRoadColIndex === -1) {
    ui.alert(`Не вдалося знайти всі необхідні стовпці в аркуші "${destinationSheetName}": 'Від', 'До', 'Відстань км.', 'Платна дорога €'. Перевірте назви.`);
    return;
  }

  let updatedRoutesCount = 0;
  let addedRoutesCount = 0;
  let skippedRoutesCount = 0;
  let detailedMessages = []; // Для логів
  let uiMessages = []; // Для повідомлень користувачу

  // Проходимо по всіх рядках вихідного аркуша, починаючи з другого (після заголовків)
  for (let i = 1; i < sourceData.length; i++) {
    const row = sourceData[i];
    const loadCity = String(row[loadCityColIndex] || '').trim();
    const unloadCity = String(row[unloadCityColIndex] || '').trim();
    const routeDistance = String(row[routeDistanceColIndex] || '').trim();
    const paidRoad = String(row[paidRoadColIndex] || '').trim();

    if (!loadCity || !unloadCity || !routeDistance || !paidRoad) {
      detailedMessages.push(`Рядок ${i + 1}: Пропущено через порожні значення (Завантаження, Розвантаження, По маршруту км, Дороги доставка).`);
      skippedRoutesCount++;
      continue;
    }

    let found = false;
    for (let j = 1; j < destData.length; j++) { // Починаємо з 1, щоб пропустити заголовки
      const destRow = destData[j];
      if (String(destRow[destFromColIndex] || '').trim() === loadCity && String(destRow[destToColIndex] || '').trim() === unloadCity) {
        const existingDistance = String(destRow[destDistanceKmColIndex] || '').trim();
        const existingPaidRoad = String(destRow[destPaidRoadColIndex] || '').trim();

        let distanceDiff = parseFloat(existingDistance) !== parseFloat(routeDistance);
        let paidRoadDiff = parseFloat(existingPaidRoad) !== parseFloat(paidRoad);

        if (distanceDiff || paidRoadDiff) {
          let alertMessage = `Маршрут ${loadCity} - ${unloadCity} вже існує, але дані відрізняються:\n`;
          if (distanceDiff) {
            alertMessage += `Відстань: ${existingDistance} км. (існуюча) vs ${routeDistance} км. (нова).\n`;
          }
          if (paidRoadDiff) {
            alertMessage += `Платна дорога: ${existingPaidRoad} € (існуюча) vs ${paidRoad} € (нова).\n`;
          }
          alertMessage += `\nПереписати маршрут новими даними?`;

          const response = ui.alert('Дані відрізняються', alertMessage, ui.ButtonSet.YES_NO);
          if (response === ui.Button.YES) {
            destinationSheet.getRange(j + 1, destDistanceKmColIndex + 1).setValue(routeDistance);
            destinationSheet.getRange(j + 1, destPaidRoadColIndex + 1).setValue(paidRoad);
            uiMessages.push(`${loadCity} - ${unloadCity}: оновлено (відстань: ${routeDistance} км., платна дорога: ${paidRoad} €).`);
            updatedRoutesCount++;
          } else {
            uiMessages.push(`${loadCity} - ${unloadCity}: існуючі дані збережено.`);
            skippedRoutesCount++;
          }
        } else {
          uiMessages.push(`${loadCity} - ${unloadCity}: вже існує і відповідає.`);
        }
        found = true;
        break;
      }
    }

    if (!found) {
      destinationSheet.appendRow([loadCity, unloadCity, routeDistance, paidRoad]);
      uiMessages.push(`${loadCity} - ${unloadCity}: додано (відстань: ${routeDistance} км., платна дорога: ${paidRoad} €).`);
      addedRoutesCount++;
    }
  }

  let summaryMessage = `Обробка завершена.\n\n`;
  summaryMessage += `Оновлено маршрутів: ${updatedRoutesCount}\n`;
  summaryMessage += `Додано маршрутів: ${addedRoutesCount}\n`;
  summaryMessage += `Пропущено маршрутів: ${skippedRoutesCount}\n\n`;
  summaryMessage += `Деталі:\n` + uiMessages.join('\n');

  ui.alert('Результати перенесення маршрутів', summaryMessage, ui.ButtonSet.OK);
}
