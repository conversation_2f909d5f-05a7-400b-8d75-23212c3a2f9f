function addRouteLinks() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName("Маршрути 2");

  if (!sheet) {
    Logger.log("Аркуш 'Маршрути 2' не знайдено.");
    return;
  }

  const range = sheet.getDataRange();
  const values = range.getValues();
  const richTextValues = range.getRichTextValues(); // Отримуємо існуючі RichTextValues

  const headers = values[0]; // Припускаємо, що перший рядок - це заголовки

  const punkt1ColIndex = headers.indexOf("Пункт 1");
  const punkt2ColIndex = headers.indexOf("Пункт 2");
  let routeColIndex = headers.indexOf("Маршрут");

  if (punkt1ColIndex === -1 || punkt2ColIndex === -1) {
    Logger.log("Один або кілька необхідних стовпців ('Пункт 1', 'Пункт 2') не знайдено.");
    return;
  }

  // Якщо стовпця "Маршрут" немає, додаємо його
  if (routeColIndex === -1) {
    sheet.insertColumnAfter(headers.length); // Додаємо стовпець в кінець
    sheet.getRange(1, headers.length + 1).setValue("Маршрут"); // Встановлюємо заголовок
    routeColIndex = headers.length; // Оновлюємо індекс
    // Оновлюємо headers та values, richTextValues, оскільки таблиця змінилася
    const newRange = sheet.getDataRange();
    values.length = 0; // Очищаємо старі значення
    Array.prototype.push.apply(values, newRange.getValues());
    richTextValues.length = 0; // Очищаємо старі значення
    Array.prototype.push.apply(richTextValues, newRange.getRichTextValues());
    headers.push("Маршрут"); // Додаємо новий заголовок
  }

  const updatedRichTextValues = [];

  for (let i = 0; i < values.length; i++) {
    const row = values[i];
    const richTextRow = richTextValues[i];
    const newRichTextRow = [];

    for (let j = 0; j < row.length; j++) {
      newRichTextRow.push(richTextRow[j]); // Копіюємо існуючі RichTextValues
    }

    if (i === 0) { // Заголовки
      updatedRichTextValues.push(newRichTextRow);
      continue;
    }

    const punkt1 = String(row[punkt1ColIndex]).trim();
    const punkt2 = String(row[punkt2ColIndex]).trim();

    if (punkt1 && punkt2) {
      const routeUrl = `https://www.google.com/maps/dir/${encodeURIComponent(punkt1)}/${encodeURIComponent(punkt2)}`;
      const routeLink = SpreadsheetApp.newRichTextValue()
        .setText("Маршрут")
        .setLinkUrl(routeUrl)
        .build();
      newRichTextRow[routeColIndex] = routeLink;
    }
    updatedRichTextValues.push(newRichTextRow);
  }

  range.setRichTextValues(updatedRichTextValues);
  Logger.log("Гіперпосилання на маршрути Google Карт успішно додано до стовпця 'Маршрут'.");
}
