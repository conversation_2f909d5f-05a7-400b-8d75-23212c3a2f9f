## Progress Update

**Дата:** 22.06.2025

### Виконані завдання:
*   Ініціалізовано Git репозиторій у папці `c:/programing/StellarAppScript`.
*   Створено файл `.gitignore` для ігнорування `node_modules/` та `.clasp.json`.
*   Підключено локальний репозиторій до віддаленого GitHub репозиторію: `https://github.com/MishaKrutiy/StellarAppScript.git`.
*   Встановлено Node.js LTS (виправлено проблему з правами адміністратора).
*   Встановлено `@google/clasp` глобально.
*   Виконано вхід у `clasp` (авторизація через Google).
*   Клоновано проект Google Apps Script з ID `"1zz-RUUIE853s7jXBYqlsinlGpnem6_yLLPcP1vDdhxsgpTjDf6GLLC9T"` у локальну папку.
*   Зафіксовано всі завантажені файли у Git репозиторії.
*   Відправлено (pushed) зміни на GitHub.
*   Витягнуто новий код App Script за допомогою `clasp pull`.
*   Оновлено `GeoNames API.js`: додано функцію `getCityCoordinates` для отримання географічних координат міст.
*   Оновлено `TollCalculator.js`:
    - Додано тестову функцію `testCalculateTollCost`.
    - Виправлено помилку `ReferenceError: getCityCoordinates is not defined` шляхом успішного `clasp push` після ввімкнення Apps Script API.
    - Виправлено помилку `Invalid value for parameter 'truck[width]'` шляхом зміни `width` на `250` (сантиметри).
    - Забезпечено успішне виконання запиту до HERE Routing API (HTTP 200 OK).
    - Виправлено помилку "unknown field `origin`" шляхом переміщення параметрів `origin` та `destination` з тіла POST-запиту до URL запиту.
    - Виявлено потенційні проблеми з розрахунком вартості платних доріг: використання фіксованих курсів валют, ризикована логіка конвертації EUR (ціна > 50), відсутність обробки інших валют, вибір мінімальної ціни без чітких критеріїв.
    - Внесено зміни до `TollCalculator.js` для покращення конвертації валют: створено об'єкт `exchangeRates` для централізованого зберігання курсів, видалено ризиковану логіку `if (price > 50)` для EUR, додано логування для невідомих валют.
    - **Виправлено параметр `width` для вантажівки на метри (з 250 см на 2.5 м).**
    - **Додано функцію `fetchExchangeRates` для динамічного отримання актуальних курсів валют через API (з запасними статичними курсами, що включають UAH та інші європейські валюти).**
    - **Змінено округлення `totalTollCost` до цілого числа.**
    - **Додано параметри транспортного засобу: `axleCount`, `co2EmissionClass`, `emissionStandard`, `axleWeight` до запиту HERE Routing API.**
    - **Тимчасово видалено параметри `co2EmissionClass` та `emissionStandard` з запиту HERE Routing API для діагностики помилки HTTP 400.**
    - **Змінено метод запиту до HERE Routing API на POST, перемістивши параметри `truck` у тіло запиту.**
*   **Створено `FillProductInfo.js`**: Додано новий скрипт для заповнення інформації про товар у вибраному рядку аркуша, з урахуванням перевірки наявності товару та його статусу "Перевірено", а також повідомлень користувачу про результат.
*   **Оновлено `Випадаюче меню.js`**: Додано пункт меню "Заповнити інформацію про товар", який викликає функцію `fillProductInformation()`.

### Поточний статус:
Проект успішно налаштований, синхронізований з GitHub та Google Apps Script. Всі файли з Apps Script завантажені та зафіксовані в репозиторії. Локальний код синхронізовано з віддаленим. Функціонал для отримання координат міст та розрахунку вартості платних доріг (з успішним запитом до HERE API) реалізовані. Виявлено та виправлено потенційні проблеми з розрахунком вартості платних доріг, пов'язані з конвертацією валют та округленням. Додано новий функціонал для заповнення інформації про товар та інтегровано його в меню Google Sheets.

### Виконані завдання:
*   Ініціалізовано Git репозиторій у папці `c:/programing/StellarAppScript`.
*   Створено файл `.gitignore` для ігнорування `node_modules/` та `.clasp.json`.
*   Підключено локальний репозиторій до віддаленого GitHub репозиторію: `https://github.com/MishaKrutiy/StellarAppScript.git`.
*   Встановлено Node.js LTS (виправлено проблему з правами адміністратора).
*   Встановлено `@google/clasp` глобально.
*   Виконано вхід у `clasp` (авторизація через Google).
*   Клоновано проект Google Apps Script з ID `"1zz-RUUIE853s7jXBYqlsinlGpnem6_yLLPcP1vDdhxsgpTjDf6GLLC9T"` у локальну папку.
*   Зафіксовано всі завантажені файли у Git репозиторії.
*   Відправлено (pushed) зміни на GitHub.
*   Витягнуто новий код App Script за допомогою `clasp pull`.
*   Оновлено `GeoNames API.js`: додано функцію `getCityCoordinates` для отримання географічних координат міст.
*   Оновлено `TollCalculator.js`:
    - Додано тестову функцію `testCalculateTollCost`.
    - Виправлено помилку `ReferenceError: getCityCoordinates is not defined` шляхом успішного `clasp push` після ввімкнення Apps Script API.
    - Виправлено помилку `Invalid value for parameter 'truck[width]'` шляхом зміни `width` на `250` (сантиметри).
    - Забезпечено успішне виконання запиту до HERE Routing API (HTTP 200 OK).
    - Виправлено помилку "unknown field `origin`" шляхом переміщення параметрів `origin` та `destination` з тіла POST-запиту до URL запиту.
    - Виявлено потенційні проблеми з розрахунком вартості платних доріг: використання фіксованих курсів валют, ризикована логіка конвертації EUR (ціна > 50), відсутність обробки інших валют, вибір мінімальної ціни без чітких критеріїв.
    - Внесено зміни до `TollCalculator.js` для покращення конвертації валют: створено об'єкт `exchangeRates` для централізованого зберігання курсів, видалено ризиковану логіку `if (price > 50)` для EUR, додано логування для невідомих валют.
    - **Виправлено параметр `width` для вантажівки на метри (з 250 см на 2.5 м).**
    - **Додано функцію `fetchExchangeRates` для динамічного отримання актуальних курсів валют через API (з запасними статичними курсами, що включають UAH та інші європейські валюти).**
    - **Змінено округлення `totalTollCost` до цілого числа.**
    - **Додано параметри транспортного засобу: `axleCount`, `co2EmissionClass`, `emissionStandard`, `axleWeight` до запиту HERE Routing API.**
    - **Тимчасово видалено параметри `co2EmissionClass` та `emissionStandard` з запиту HERE Routing API для діагностики помилки HTTP 400.**
    - **Змінено метод запиту до HERE Routing API на POST, перемістивши параметри `truck` у тіло запиту.**
*   **Створено `FillProductInfo.js`**: Додано новий скрипт для заповнення інформації про товар у вибраному рядку аркуша, з урахуванням перевірки наявності товару та його статусу "Перевірено", а також повідомлень користувачу про результат.
*   **Оновлено `Випадаюче меню.js`**: Додано пункт меню "Заповнити інформацію про товар", який викликає функцію `fillProductInformation()`.
*   **Оновлено `ЗбереженняМаршрутів.js`**: Додано функціонал для розрахунку та збереження вартості платних доріг у стовпці "Платна дорога € розрахована" аркуша "Маршрути 3" при збереженні маршруту.
*   **Оновлено `ЗаповненняВідстаней.js`**: Додано логіку для запису вартості платних доріг (з пріоритетом ручного введення над розрахованим значенням) у стовпці "Дороги з вантажем" та "Дороги порожній" активного аркуша, сумуючи їх за категоріями маршрутних сегментів.

### Поточний статус:
Проект успішно налаштований, синхронізований з GitHub та Google Apps Script. Всі файли з Apps Script завантажені та зафіксовані в репозиторії. Локальний код синхронізовано з віддаленим. Функціонал для отримання координат міст та розрахунку вартості платних доріг (з успішним запитом до HERE API) реалізовані. Виявлено та виправлено потенційні проблеми з розрахунком вартості платних доріг, пов'язані з конвертацією валют та округленням. Додано новий функціонал для заповнення інформації про товар та інтегровано його в меню Google Sheets. Функція збереження маршрутів тепер автоматично розраховує вартість платних доріг. Функція заповнення відстаней тепер також заповнює вартість платних доріг, сумуючи їх за категоріями "з вантажем" та "порожній".

### Наступні кроки:
- Протестувати оновлену функцію `calculateTollCost` з різними маршрутами та валютами, щоб перевірити коректність розрахунків.
- Протестувати функцію `fillProductInformation` у Google Sheets з різними сценаріями (товар знайдено/не знайдено, перевірено/не перевірено).
- Протестувати оновлену функцію `saveRouteDistances` для перевірки коректності розрахунку та збереження вартості платних доріг.
- Протестувати оновлену функцію `fillDistancesFromRoutes` для перевірки коректності заповнення відстаней та вартості платних доріг.
