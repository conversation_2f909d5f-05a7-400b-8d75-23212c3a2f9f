/**
 * Розраховує вартість платних доріг для фури між двома містами за допомогою HERE Routing API.
 *
 * @param {string} startCity Назва міста відправлення.
 * @param {string} endCity Назва міста прибуття.
 * @returns {number|null} Розрахована вартість платних доріг у євро або null у разі помилки.
 */
function calculateTollCost(startCity, endCity) {
  const apiKey = '79JmNq5MGllWma25lLzmr4-a_8LYPbjqxGFMjl_dHaY';

  // Отримання координат міст за допомогою GeoNames API
  const startCoords = getCityCoordinates(startCity);
  const endCoords = getCityCoordinates(endCity);

  if (!startCoords || !endCoords) {
    Logger.log(`Помилка: Не вдалося отримати координати для міст ${startCity} або ${endCity}`);
    return null;
  }

  // Параметри фури (40-тонна вантажівка)
  const vehicleSpecs = {
    length: 17000,        // метри
    width: 250,        // сантиметри (2.5 метри)
    height: 400,         // метри
    grossWeight: 40000 // кг (40 тонн)
  };

  // Актуальні курси валют до EUR (приблизні, для прикладу)
  const exchangeRates = {
    'PLN': 4.3, // 1 EUR ≈ 4.3 PLN
    'CZK': 25,  // 1 EUR ≈ 25 CZK
    'HUF': 400  // 1 EUR ≈ 400 HUF
  };

  // Формування URL для HERE Routing API
  const apiUrl = `https://router.hereapi.com/v8/routes?transportMode=truck&origin=${startCoords.lat},${startCoords.lng}&destination=${endCoords.lat},${endCoords.lng}&return=summary,tolls,polyline&apiKey=${apiKey}&truck[length]=${vehicleSpecs.length}&truck[width]=${vehicleSpecs.width}&truck[height]=${vehicleSpecs.height}&truck[grossWeight]=${vehicleSpecs.grossWeight}`;

  try {
    const response = UrlFetchApp.fetch(apiUrl, { method: 'get', muteHttpExceptions: true });
    const responseCode = response.getResponseCode();

    if (responseCode >= 200 && responseCode < 300) {
      const data = JSON.parse(response.getContentText());

      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0];
        let totalTollCost = 0;

        // Обробляємо кожну секцію маршруту
        let totalTollRoads = 0;
        if (route.sections) {
          route.sections.forEach(section => {
            if (section.tolls && Array.isArray(section.tolls)) {
              totalTollRoads += section.tolls.length;
              // Обробляємо кожну платну дорогу в секції
              section.tolls.forEach((toll, tollIndex) => {
                if (toll.fares && Array.isArray(toll.fares)) {
                  // Знаходимо мінімальну ціну серед всіх тарифів для цієї платної дороги
                  let minPrice = null;
                  let allPrices = [];

                  toll.fares.forEach(fare => {
                    if (fare.price && fare.price.value !== undefined && fare.price.value !== null) {
                      let price = parseFloat(fare.price.value);
                      const currency = fare.price.currency || 'EUR';

                      // Конвертуємо в євро залежно від валюти
                      let priceInEur = price;
                      if (exchangeRates[currency]) {
                        priceInEur = price / exchangeRates[currency];
                        Logger.log(`  Тариф: ${price} ${currency} (${priceInEur.toFixed(2)} EUR)`);
                      } else if (currency === 'EUR') {
                        Logger.log(`  Тариф: ${price} EUR`);
                      } else {
                        Logger.log(`  Тариф: ${price} ${currency} (невідома валюта, залишаємо як є)`);
                      }

                      allPrices.push(priceInEur);
                      if (minPrice === null || priceInEur < minPrice) {
                        minPrice = priceInEur;
                      }
                    }
                  });

                  // Логування для діагностики
                  if (allPrices.length > 0) {
                    Logger.log(`Платна дорога ${tollIndex + 1}: тарифи [${allPrices.map(p => p.toFixed(2)).join(', ')}] EUR, обрано мінімальний: ${minPrice.toFixed(2)} EUR`);
                  }

                  // Додаємо тільки мінімальну ціну для цієї платної дороги
                  if (minPrice !== null) {
                    totalTollCost += minPrice;
                  }
                }
              });
            }
          });
        }

        Logger.log(`Знайдено ${totalTollRoads} платних доріг на маршруті`);
        Logger.log(`Розрахована вартість платних доріг: ${totalTollCost} євро`);
        return Math.round(totalTollCost * 100) / 100; // Округлюємо до 2 знаків після коми

      } else {
        Logger.log(`Не вдалося знайти маршрут для ${startCity} до ${endCity}`);
        return null;
      }
    } else {
      Logger.log(`Помилка HTTP при виклику HERE API: ${responseCode}`);
      return null;
    }
  } catch (e) {
    Logger.log(`Помилка при виклику HERE API: ${e.message}`);
    return null;
  }
}

/**
 * Простий тест функції
 */
function testTollCalculation() {
  const cost = calculateTollCost("antverpen", "Bari Італія");
  Logger.log(`Вартість платних доріг Київ-Берлін: ${cost} євро`);
}
