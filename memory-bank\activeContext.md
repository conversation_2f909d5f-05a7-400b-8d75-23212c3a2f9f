# Active Context

## Current Work Focus
Поточне завдання полягає в налагодженні функціоналу розрахунку вартості платних доріг для фур між містами за допомогою HERE Routing API, а також у розробці функціоналу для заповнення інформації про товар у Google Sheets.

## Recent Changes
- Створено `projectbrief.md` з оглядом проекту, цілями та обсягом.
- Створено `productContext.md` з описом призначення, вирішених проблем, очікуваної роботи та цілей користувацького досвіду.
- Створено `systemPatterns.md` з інформацією про архітектуру системи, ключові технічні рішення, шаблони проектування та взаємозв'язки компонентів.
- Створено `techContext.md` з деталями про використовувані технології, налаштування розробки, технічні обмеження, залежності та шаблони використання інструментів.
- Оновлено `GeoNames API.js`: додано функцію `getCityCoordinates` для отримання географічних координат міст.
- Оновлено `TollCalculator.js`:
    - Додано тестову функцію `testCalculateTollCost`.
    - Виправлено помилку `ReferenceError: getCityCoordinates is not defined` шляхом успішного `clasp push` після ввімкнення Apps Script API.
    - Виправлено помилку `Invalid value for parameter 'truck[width]'` шляхом зміни `width` на `250` (сантиметри).
    - Забезпечено успішне виконання запиту до HERE Routing API (HTTP 200 OK).
    - Виправлено помилку "unknown field `origin`" шляхом переміщення параметрів `origin` та `destination` з тіла POST-запиту до URL запиту.
    - Виявлено потенційні проблеми з розрахунком вартості платних доріг: використання фіксованих курсів валют, ризикована логіка конвертації EUR (ціна > 50), відсутність обробки інших валют, вибір мінімальної ціни без чітких критеріїв.
    - Внесено зміни до `TollCalculator.js` для покращення конвертації валют: створено об'єкт `exchangeRates` для централізованого зберігання курсів, видалено ризиковану логіку `if (price > 50)` для EUR, додано логування для невідомих валют.
    - **Отримано зворотний зв'язок від користувача щодо проблеми з "центами" для EUR, яка, можливо, не була повністю вирішена, та підтвердження, що код розраховує суму всіх платних доріг на одному маршруті.**
    - **Виправлено параметр `width` для вантажівки на метри (з 250 см на 2.5 м).**
    - **Додано функцію `fetchExchangeRates` для динамічного отримання актуальних курсів валют через API (з запасними статичними курсами, що включають UAH та інші європейські валюти).**
    - **Змінено округлення `totalTollCost` до цілого числа.**
    - **Додано параметри транспортного засобу: `axleCount`, `co2EmissionClass`, `emissionStandard`, `axleWeight` до запиту HERE Routing API.**
    - **Тимчасово видалено параметри `co2EmissionClass` та `emissionStandard` з запиту HERE Routing API для діагностики помилки HTTP 400.**
    - **Змінено метод запиту до HERE Routing API на POST, перемістивши параметри `truck` у тіло запиту.**
    - **Повернуто метод запиту до HERE Routing API на GET, перемістивши всі параметри `truck` (включаючи `co2EmissionClass` та `emissionStandard`) назад у URL запиту, згідно з документацією.**
- **Створено `FillProductInfo.js`**: Додано новий скрипт для заповнення інформації про товар у вибраному рядку аркуша, з урахуванням перевірки наявності товару та його статусу "Перевірено", а також повідомлень користувачу про результат.

## Next Steps
- Протестувати оновлену функцію `calculateTollCost` з різними маршрутами та валютами, щоб перевірити коректність розрахунків.
- Протестувати функцію `fillProductInformation` у Google Sheets з різними сценаріями (товар знайдено/не знайдено, перевірено/не перевірено).
- Оновити `progress.md` з деталями виконаних завдань.

## Active Decisions and Considerations
- Використання GeoNames API для отримання координат міст є ключовим для будь-якого сервісу розрахунку маршрутів/доріг.
- HERE Routing API інтегровано для розрахунку вартості платних доріг.
- Параметри `origin` та `destination` передаються в URL, а параметри `truck` передаються в URL запиту.
- Прийнято рішення про централізацію курсів валют в об'єкті `exchangeRates` для легшого управління та оновлення.
- Логіка конвертації EUR спрощена, припускаючи, що API повертає ціни в євро, якщо не вказано інше.
- **Необхідно підтвердити, як HERE API повертає ціни в EUR (євро чи центи), щоб уникнути помилок у розрахунках.**
- **Реалізовано повідомлення користувачу для сценаріїв, коли товар не знайдено або не перевірено.**

## Important Patterns and Preferences
- Проект використовує Google Apps Script, що означає, що функції можуть бути глобально доступними або викликатися через меню/тригери в Google Sheets.
- Взаємодія з Google Sheets є ключовою.

## Learnings and Project Insights
- Проект є колекцією скриптів, що працюють разом для автоматизації логістичних та комерційних завдань.
- Залежність від Google Apps Script та зовнішніх API є центральною.
- Критично важливо ретельно перевіряти документацію API щодо очікуваного формату запитів (GET/POST) та розташування параметрів (URL/тіло запиту), особливо для складних API, таких як HERE Routing API. Помилки в цьому можуть призвести до "unknown field" або "malformed request".
- Точність розрахунку вартості платних доріг сильно залежить від актуальності курсів валют та коректної інтерпретації даних, що повертаються API.
