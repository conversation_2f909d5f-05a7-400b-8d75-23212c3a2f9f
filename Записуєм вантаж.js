/**
 * Заповнює інформацію про вантаж у вибраному рядку активного аркуша,
 * використовуючи дані з аркуша "Вантаж".
 */
function fillCargoInformation() {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const activeSheet = spreadsheet.getActiveSheet();
  const activeRange = activeSheet.getActiveRange();
  const activeRow = activeRange.getRow();

  const cargoSheetName = "Вантаж";
  const cargoSheet = spreadsheet.getSheetByName(cargoSheetName);

  if (!cargoSheet) {
    SpreadsheetApp.getUi().alert("Помилка", `Аркуш '${cargoSheetName}' не знайдено. Будь ласка, переконайтеся, що він існує.`, SpreadsheetApp.getUi().ButtonSet.OK);
    return;
  }

  const activeSheetHeaders = activeSheet.getRange(1, 1, 1, activeSheet.getLastColumn()).getValues()[0];
  const cargoSheetHeaders = cargoSheet.getRange(1, 1, 1, cargoSheet.getLastColumn()).getValues()[0];

  const cargoColumnName = "Вантаж";
  const checkedColumnName = "Перевірено";
  const columnsToFill = ["1-24", "ADR", "Скоропорт", "Підігрів", "Подвійне миття", "Перекладач", "Ветеринар"];

  const cargoColumnIndexActive = activeSheetHeaders.indexOf(cargoColumnName);
  const cargoColumnIndexCargo = cargoSheetHeaders.indexOf(cargoColumnName);
  const checkedColumnIndexCargo = cargoSheetHeaders.indexOf(checkedColumnName);

  if (cargoColumnIndexActive === -1) {
    SpreadsheetApp.getUi().alert("Помилка", `Стовпець '${cargoColumnName}' не знайдено в активному аркуші.`, SpreadsheetApp.getUi().ButtonSet.OK);
    return;
  }
  if (cargoColumnIndexCargo === -1) {
    SpreadsheetApp.getUi().alert("Помилка", `Стовпець '${cargoColumnName}' не знайдено в аркуші '${cargoSheetName}'.`, SpreadsheetApp.getUi().ButtonSet.OK);
    return;
  }
  if (checkedColumnIndexCargo === -1) {
    SpreadsheetApp.getUi().alert("Помилка", `Стовпець '${checkedColumnName}' не знайдено в аркуші '${cargoSheetName}'.`, SpreadsheetApp.getUi().ButtonSet.OK);
    return;
  }

  const cargoValue = activeSheet.getRange(activeRow, cargoColumnIndexActive + 1).getValue();

  if (!cargoValue) {
    SpreadsheetApp.getUi().alert("Помилка", "Будь ласка, виберіть рядок, що містить значення у стовпці 'Вантаж'.", SpreadsheetApp.getUi().ButtonSet.OK);
    return;
  }

  const productData = productSheet.getDataRange().getValues();
  let foundProductRow = null;

  for (let i = 1; i < productData.length; i++) { // Починаємо з 1, щоб пропустити заголовки
    if (productData[i][cargoColumnIndexProduct] == cargoValue) {
      foundProductRow = productData[i];
      break;
    }
  }

  if (!foundProductRow) {
    SpreadsheetApp.getUi().alert("Помилка", `Товар '${cargoValue}' не знайдено в аркуші '${productSheetName}'.`, SpreadsheetApp.getUi().ButtonSet.OK);
    return;
  }

  const isChecked = String(foundProductRow[checkedColumnIndexProduct]).toUpperCase() === "TRUE";

  if (!isChecked) {
    SpreadsheetApp.getUi().alert("Помилка", `Товар '${cargoValue}' не перевірено. Заповнення неможливе.`, SpreadsheetApp.getUi().ButtonSet.OK);
    return;
  }

  columnsToFill.forEach(colName => {
    const productColIndex = productSheetHeaders.indexOf(colName);
    const activeColIndex = activeSheetHeaders.indexOf(colName);

    if (productColIndex !== -1 && activeColIndex !== -1) {
      const valueToCopy = foundProductRow[productColIndex];
      activeSheet.getRange(activeRow, activeColIndex + 1).setValue(valueToCopy);
    } else {
      console.warn(`Стовпець '${colName}' не знайдено в одному з аркушів. Пропущено.`);
    }
  });

  SpreadsheetApp.getUi().alert("Успіх", `Інформацію про товар '${cargoValue}' успішно заповнено.`, SpreadsheetApp.getUi().ButtonSet.OK);
}
