function addCityMapLinks() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.getSheetByName("Маршрути 2");

  if (!sheet) {
    Logger.log("Аркуш 'Маршрути 2' не знайдено.");
    return;
  }

  const range = sheet.getDataRange();
  const values = range.getValues();
  const richTextValues = range.getRichTextValues(); // Отримуємо існуючі RichTextValues

  const headers = values[0]; // Припускаємо, що перший рядок - це заголовки

  const punkt1ColIndex = headers.indexOf("Пункт 1");
  const punkt2ColIndex = headers.indexOf("Пункт 2");

  if (punkt1ColIndex === -1 || punkt2ColIndex === -1) {
    Logger.log("Один або кілька необхідних стовпців ('Пункт 1', 'Пункт 2') не знайдено.");
    return;
  }

  const updatedRichTextValues = [];

  // Починаємо з 1, щоб пропустити заголовки
  for (let i = 0; i < values.length; i++) {
    const row = values[i];
    const richTextRow = richTextValues[i];
    const newRichTextRow = [];

    for (let j = 0; j < row.length; j++) {
      let cellValue = row[j];
      let newRichTextValue = richTextRow[j];

      if (i === 0) { // Заголовки залишаємо без змін
        newRichTextRow.push(newRichTextValue);
        continue;
      }

      if (j === punkt1ColIndex || j === punkt2ColIndex) {
        const cityName = String(cellValue).trim();
        if (cityName) {
          const mapUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(cityName)}`;
          newRichTextValue = SpreadsheetApp.newRichTextValue()
            .setText(cityName)
            .setLinkUrl(mapUrl)
            .build();
        }
      }
      newRichTextRow.push(newRichTextValue);
    }
    updatedRichTextValues.push(newRichTextRow);
  }

  range.setRichTextValues(updatedRichTextValues);
  Logger.log("Гіперпосилання на Google Карти успішно додано до стовпців 'Пункт 1' та 'Пункт 2'.");
}
