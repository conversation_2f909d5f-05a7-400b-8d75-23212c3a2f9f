# System Patterns

## System Architecture
Проект складається з окремих файлів Google Apps Script, які, ймовірно, взаємодіють між собою через глобальні функції або спільні дані в Google Sheets. Це модульна архітектура, де кожен скрипт відповідає за певну функціональність.

## Key Technical Decisions
- Використання Google Apps Script для автоматизації.
- Можлива інтеграція з Google Sheets як основною базою даних та інтерфейсом.
- Використання зовнішніх API (GeoNames) для отримання географічних даних.

## Design Patterns in Use
- **Модульність**: Кожен файл скрипта, схоже, реалізує окремий модуль або функцію.
- **Функціональний підхід**: Ймовірно, більшість логіки реалізована через функції, які викликаються за подіями або вручну.

## Component Relationships
- **Основні скрипти**: `Код.js`, `appsscript.json` (для конфігурації проекту Apps Script).
- **Скрипти для маршрутів**: `УнікальніМаршрути.js`, `ЗбереженняМаршрутів.js`, `ПеренесенняМаршрутівТрімекс.js`, `addRouteLinks.js`, `createUniqueRoutes.js`.
- **Скрипти для міст/геоданих**: `ЗаповненняВідстаней.js`, `GeoNames API.js`, `addCityMapLinks.js`.
- **Скрипти для комерційних пропозицій**: `записуєм комерційну пропозиціюю.js`, `Випадаюче меню.js`.

## Critical Implementation Paths
- **Обробка даних**: Читання та запис даних з Google Sheets.
- **Виклики API**: Взаємодія з GeoNames API.
- **Генерація посилань**: Створення посилань на карти.
- **Логіка маршрутів**: Розрахунок та оптимізація маршрутів.
