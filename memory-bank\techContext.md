# Tech Context

## Technologies Used
- **Google Apps Script (GAS)**: Основна платформа для розробки. Це JavaScript-подібне середовище, що працює на серверах Google.
- **JavaScript**: Мова програмування, що використовується в GAS.
- **Google Sheets API**: Для взаємодії з електронними таблицями Google Sheets.
- **GeoNames API**: Зовнішній API для отримання географічних даних (назви міст, координати).
- **Google Maps API (можливо)**: Для генерації посилань на карти.

## Development Setup
- Розробка відбувається безпосередньо в середовищі Google Apps Script (script.google.com).
- Файли синхронізуються з локальною файловою системою, що дозволяє використовувати VSCode для редагування.
- `appsscript.json` є файлом маніфесту, що містить конфігурацію проекту Apps Script (наприклад, дозволи, сервіси).

## Technical Constraints
- **Обмеження Google Apps Script**: Час виконання, кількість викликів API, розмір даних.
- **Обмеження зовнішніх API**: Ліміти запитів до GeoNames API.
- **Продуктивність**: Скрипти можуть бути повільними при обробці великих обсягів даних через особливості виконання GAS.

## Dependencies
- Внутрішні сервіси Google (SpreadsheetApp, UrlFetchApp).
- Зовнішні API (GeoNames).

## Tool Usage Patterns
- Використання `UrlFetchApp` для HTTP-запитів до зовнішніх API.
- Використання `SpreadsheetApp` для доступу та маніпуляцій з даними в Google Sheets.
- Можливе використання тригерів Apps Script (наприклад, `onOpen`, `onEdit`) для автоматичного запуску функцій.
