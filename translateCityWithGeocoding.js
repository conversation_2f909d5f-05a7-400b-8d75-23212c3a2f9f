function translateCityNamesFromMaps() {
  const apiKey = 'AIzaSyAihLb-pXhvGV6iaXTbtBvyaE6pcNqIu2Q'; // 🔐 ← встав свій обмежений ключ
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];

  const cityCol = headers.indexOf('city_ascii') + 1;
  const countryCol = headers.indexOf('country') + 1;
  
  if (cityCol === 0 || countryCol === 0) {
    Logger.log("❌ Не знайдено необхідних колонок.");
    return;
  }

  // Знайти або створити колонку для перекладу
  let nameUkCol = headers.indexOf('Назва Українською') + 1;
  if (nameUkCol === 0) {
    nameUkCol = headers.length + 1;
    sheet.getRange(1, nameUkCol).setValue('Назва Українською');
  }

  const lastRow = sheet.getLastRow();
  const cities = sheet.getRange(2, cityCol, lastRow - 1, 1).getValues();
  const countries = sheet.getRange(2, countryCol, lastRow - 1, 1).getValues();
  const translatedNames = sheet.getRange(2, nameUkCol, lastRow - 1, 1).getValues();

  for (let i = 0; i < cities.length; i++) {
    const city = cities[i][0];
    const country = countries[i][0];
    const alreadyTranslated = translatedNames[i][0];

    if (!city || !country || alreadyTranslated) continue; // ⛔ Пропустити, якщо вже перекладено або бракує даних

    const query = `${city}, ${country}`;
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(query)}&key=${apiKey}&language=uk`;

    try {
      const response = UrlFetchApp.fetch(url);
      const result = JSON.parse(response.getContentText());

      if (result.status === "OK" && result.results.length > 0) {
        const data = result.results[0];
        const cityNameUk = data.address_components[0].long_name;

        sheet.getRange(i + 2, nameUkCol).setValue(cityNameUk); // 🟢 Запис у таблицю
        Logger.log(`✅ ${query} → ${cityNameUk}`);
      } else {
        Logger.log(`⚠️ Немає результату для: ${query}`);
      }

    } catch (e) {
      Logger.log(`❌ Помилка "${query}": ${e}`);
    }

    Utilities.sleep(10); // 💤 Захист від ліміту API
  }
}
