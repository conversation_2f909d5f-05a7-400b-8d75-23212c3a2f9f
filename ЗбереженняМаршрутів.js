/**
 * @OnlyCurrentDoc
 */

function saveRouteDistances() {
  const ui = SpreadsheetApp.getUi();
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const activeSheet = spreadsheet.getActiveSheet();
  const activeRange = activeSheet.getActiveRange();
  const activeRowIndex = activeRange.getRow();

  // 1. Перевірка, чи вибрано рівно один рядок
  if (activeRange.getNumRows() !== 1) {
    ui.alert('Будь ласка, виберіть один рядок для обробки.');
    return;
  }

  // Отримуємо всі значення з активного рядка, незалежно від вибраної клітинки
  const rowValues = activeSheet.getRange(activeRowIndex, 1, 1, activeSheet.getLastColumn()).getValues()[0];
  // Logger.log(`Сирі значення з активного рядка (повний рядок): ${JSON.stringify(rowValues)}`); // Вимкнено для скорочення логів

  const headers = activeSheet.getRange(1, 1, 1, activeSheet.getLastColumn()).getValues()[0].map(h => String(h).trim());
  // Logger.log(`Заголовки активного аркуша: ${headers}`); // Вимкнено для скорочення логів

  // 3. Доступ до аркуша "Маршрути 3"
  const routesSheet = spreadsheet.getSheetByName("Маршрути 3");
  if (!routesSheet) {
    ui.alert('Аркуш "Маршрути 3" не знайдено. Будь ласка, створіть його.');
    return;
  }

  // 4. Отримання даних та заголовків з аркуша "Маршрути 3"
  const routesData = routesSheet.getDataRange().getValues();
  const routesHeaders = routesData.length > 0 ? routesData[0].map(h => String(h).trim()) : [];
  // Logger.log(`Заголовки аркуша "Маршрути 3": ${routesHeaders}`); // Вимкнено для скорочення логів

  const fromColIndex = routesHeaders.indexOf('Від');
  const toColIndex = routesHeaders.indexOf('До');
  const distanceKmColIndex = routesHeaders.indexOf('Відстань км.');
  const tollCostColIndex = routesHeaders.indexOf('Платна дорога € розрахована'); // Додано новий стовпець

  if (fromColIndex === -1 || toColIndex === -1 || distanceKmColIndex === -1 || tollCostColIndex === -1) {
    ui.alert('Не вдалося знайти всі необхідні стовпці в аркуші "Маршрути 3": "Від", "До", "Відстань км.", "Платна дорога € розрахована". Перевірте назви.');
    return;
  }

  // Допоміжна функція для обробки маршруту
  function processRoute(sourceColName, destColName, distColName) {
    const sourceColIndex = headers.indexOf(sourceColName);
    const destColIndex = headers.indexOf(destColName);
    const distColIndex = headers.indexOf(distColName);

    if (sourceColIndex === -1 || destColIndex === -1 || distColIndex === -1) {
      // Logger.log(`Пропущено маршрут: Не вдалося знайти всі необхідні стовпці: '${sourceColName}', '${destColName}', '${distColName}'.`); // Вимкнено для скорочення логів
      return `Не знайдено стовпці для ${sourceColName} - ${destColName}.`;
    }

    const sourceCity = String(rowValues[sourceColIndex] || '').trim();
    const destinationCity = String(rowValues[destColIndex] || '').trim();
    const distance = String(rowValues[distColIndex] || '').trim();

    // Logger.log(`Зчитані та оброблені значення для маршруту '${sourceColName}' - '${destColName}': Від='${sourceCity}', До='${destinationCity}', Відстань='${distance}'`); // Вимкнено для скорочення логів

    if (!sourceCity || !destinationCity || !distance) {
      // Logger.log(`Пропущено маршрут: Одне або кілька необхідних значень для маршруту '${sourceColName}' - '${destColName}' порожні.`); // Вимкнено для скорочення логів
      return `Порожні значення для ${sourceColName} - ${destColName}.`;
    }

    let found = false;
    for (let i = 1; i < routesData.length; i++) {
      const row = routesData[i];
      if (row[fromColIndex] !== undefined && row[toColIndex] !== undefined) {
        if (String(row[fromColIndex]).trim() === sourceCity && String(row[toColIndex]).trim() === destinationCity) {
          const existingDistance = String(row[distanceKmColIndex] || '').trim();
          if (parseFloat(existingDistance) !== parseFloat(distance)) {
            const response = ui.alert(
              'Відстань відрізняється',
              `Маршрут ${sourceCity} - ${destinationCity}:\nІснуюча відстань: ${existingDistance} км.\nНова відстань: ${distance} км.\n\nПереписати маршрут новою відстанню?`,
              ui.ButtonSet.YES_NO
            );
            if (response === ui.Button.YES) {
              routesSheet.getRange(i + 1, distanceKmColIndex + 1).setValue(distance); // i+1 для рядка, distanceKmColIndex+1 для стовпця
              const tollCost = calculateTollCost(sourceCity, destinationCity);
              if (tollCost !== null) {
                routesSheet.getRange(i + 1, tollCostColIndex + 1).setValue(tollCost);
              } else {
                routesSheet.getRange(i + 1, tollCostColIndex + 1).setValue('не розраховано');
              }
              return `${sourceCity} - ${destinationCity} - ${distance} км. (оновлено). Вартість платних доріг: ${tollCost !== null ? tollCost + ' €' : 'не розраховано'}.`;
            } else {
              return `${sourceCity} - ${destinationCity} - ${existingDistance} км. (існуюча, збережено).`;
            }
          } else {
            // Якщо відстань не змінилася, але потрібно оновити вартість платних доріг (наприклад, якщо вона була порожньою)
            const existingTollCost = String(row[tollCostColIndex] || '').trim();
            if (!existingTollCost || existingTollCost === 'не розраховано') {
              const tollCost = calculateTollCost(sourceCity, destinationCity);
              if (tollCost !== null) {
                routesSheet.getRange(i + 1, tollCostColIndex + 1).setValue(tollCost);
                return `${sourceCity} - ${destinationCity} - ${distance} км. (існує, відповідає). Вартість платних доріг оновлено: ${tollCost} €.`;
              } else {
                return `${sourceCity} - ${destinationCity} - ${distance} км. (існує, відповідає). Вартість платних доріг: не розраховано.`;
              }
            }
            return `${sourceCity} - ${destinationCity} - ${distance} км. (існує, відповідає).`;
          }
          found = true;
          break;
        }
      }
    }

    if (!found) {
      const tollCost = calculateTollCost(sourceCity, destinationCity);
      const newRow = new Array(routesHeaders.length).fill(''); // Створюємо порожній масив потрібного розміру
      newRow[fromColIndex] = sourceCity;
      newRow[toColIndex] = destinationCity;
      newRow[distanceKmColIndex] = distance;
      if (tollCost !== null) {
        newRow[tollCostColIndex] = tollCost;
      }
      routesSheet.appendRow(newRow);
      return `${sourceCity} - ${destinationCity} - ${distance} км. (додано). Вартість платних доріг: ${tollCost !== null ? tollCost + ' €' : 'не розраховано'}.`;
    }
  }

  let results = [];
  results.push(processRoute('Подача', 'Завантаження', 'Подача км.'));
  results.push(processRoute('Завантаження', 'Кордон', 'До кордону км.'));
  results.push(processRoute('Кордон', 'Розвантаження', 'Після кордону км.'));
  results.push(processRoute('Розвантаження', 'Повернення', 'Повернення км.'));

  ui.alert('Результати обробки маршрутів:\n\n' + results.filter(r => r).join('\n'));
}
